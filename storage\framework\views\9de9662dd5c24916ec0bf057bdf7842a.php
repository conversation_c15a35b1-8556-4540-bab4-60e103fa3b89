<?php $__env->startSection('title', 'Manajemen Retur - Dashboard Supplier'); ?>
<?php $__env->startSection('page-title', 'Manajemen Retur'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Manajemen Retur</h1>
                    <p class="text-gray-600 mt-1">Ke<PERSON>la permintaan retur dan pengiriman yang dibatalkan</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['total_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['requested_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Diminta</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['approved_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Disetujui</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['completed_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Selesai</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['cancelled_deliveries'])); ?></div>
            <div class="supplier-dashboard-stat-label">Pengiriman Dibatalkan</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month"
                           name="month"
                           value="<?php echo e($filterMonth); ?>"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text"
                           name="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Cari produk..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Permintaan Retur</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Toko</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Tanggal Retur</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->product->name ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->store->name ?? 'Gudang Pusat'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($return->quantity)); ?> unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600"><?php echo e($return->reason_in_indonesian); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <?php if($return->status === 'requested'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Diminta
                                    </span>
                                <?php elseif($return->status === 'approved'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Disetujui
                                    </span>
                                <?php elseif($return->status === 'completed'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Selesai
                                    </span>
                                <?php elseif($return->status === 'rejected'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Ditolak
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->return_date ? $return->return_date->format('d M Y') : 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('supplier.returns.show', $return)); ?>"
                                       class="supplier-dashboard-btn supplier-dashboard-btn-secondary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Detail
                                    </a>
                                    <?php if($return->status === 'approved'): ?>
                                    <button onclick="openRespondModal('<?php echo e($return->id); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                        </svg>
                                        Respons
                                    </button>
                                    <?php endif; ?>
                                    <button onclick="openDeleteReturnModal('<?php echo e($return->id); ?>', '<?php echo e($return->product->name ?? 'N/A'); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-danger text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus
                                    </button>
                                    <button onclick="openResendReturnModal('<?php echo e($return->id); ?>', '<?php echo e($return->product->name ?? 'N/A'); ?>', '<?php echo e($return->quantity); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Kirim Ulang
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada permintaan retur</p>
                                    <p>Permintaan retur dari toko akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Cancelled Deliveries Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Pengiriman yang Dibatalkan</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Harga Satuan</th>
                            <th class="px-6 py-3">Total Harga</th>
                            <th class="px-6 py-3">Tanggal Pengiriman</th>
                            <th class="px-6 py-3">Catatan</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $cancelledDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->product->name ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($delivery->quantity)); ?> unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp <?php echo e(number_format($delivery->unit_price, 0, ',', '.')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp <?php echo e(number_format($delivery->total_price, 0, ',', '.')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->delivery_date ? $delivery->delivery_date->format('d M Y') : 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600"><?php echo e($delivery->notes ?? 'Tidak ada catatan'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="openDeleteCancelledModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name ?? 'N/A'); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-danger text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus
                                    </button>
                                    <button onclick="openResendModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name ?? 'N/A'); ?>', '<?php echo e($delivery->quantity); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Kirim Ulang
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman yang dibatalkan</p>
                                    <p>Pengiriman yang dibatalkan akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>

<!-- Response Modal -->
<div id="responseModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Respons Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeRespondModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="responseForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Aksi</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="action" value="accept" class="mr-2" required>
                                <span class="text-green-600 font-medium">Terima Retur</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="action" value="reject" class="mr-2" required>
                                <span class="text-red-600 font-medium">Tolak Retur</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label for="supplier_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="supplier_notes"
                                  name="supplier_notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Tambahkan catatan untuk respons ini..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeRespondModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Kirim Respons
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Resend Cancelled Delivery Modal -->
<div id="resendModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Kirim Ulang Pengiriman</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeResendModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="resendForm" method="POST">
            <?php echo csrf_field(); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-600 mb-4">
                            Buat pengiriman baru berdasarkan pengiriman yang dibatalkan untuk produk: <strong id="resendProductName"></strong>
                        </p>
                        <p class="text-sm text-gray-600 mb-4">
                            Jumlah: <strong id="resendQuantity"></strong> unit
                        </p>
                    </div>

                    <div>
                        <label for="resend_delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman Baru <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="resend_delivery_date"
                               name="delivery_date"
                               min="<?php echo e(date('Y-m-d')); ?>"
                               class="supplier-dashboard-input"
                               required>
                    </div>

                    <div>
                        <label for="resend_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="resend_notes"
                                  name="notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Catatan untuk pengiriman ulang..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeResendModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Buat Pengiriman Baru
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Cancelled Delivery Modal -->
<div id="deleteCancelledModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Hapus Pengiriman yang Dibatalkan</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteCancelledModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="deleteCancelledForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penghapusan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menghapus pengiriman yang dibatalkan untuk produk <strong id="deleteProductName"></strong>?
                            Tindakan ini tidak dapat dibatalkan.
                        </p>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeDeleteCancelledModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    Ya, Hapus Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Return Modal -->
<div id="deleteReturnModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Hapus Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteReturnModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="deleteReturnForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penghapusan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menghapus retur untuk produk <strong id="deleteReturnProductName"></strong>?
                            Tindakan ini tidak dapat dibatalkan.
                        </p>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeDeleteReturnModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    Ya, Hapus Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Resend Return Modal -->
<div id="resendReturnModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Kirim Ulang Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeResendReturnModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="resendReturnForm" method="POST">
            <?php echo csrf_field(); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-600 mb-4">
                            Buat retur baru berdasarkan retur yang ada untuk produk: <strong id="resendReturnProductName"></strong>
                        </p>
                        <p class="text-sm text-gray-600 mb-4">
                            Jumlah: <strong id="resendReturnQuantity"></strong> unit
                        </p>
                    </div>

                    <div>
                        <label for="resend_return_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Retur Baru <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="resend_return_date"
                               name="return_date"
                               min="<?php echo e(date('Y-m-d')); ?>"
                               class="supplier-dashboard-input"
                               required>
                    </div>

                    <div>
                        <label for="resend_return_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Retur <span class="text-red-500">*</span>
                        </label>
                        <select id="resend_return_reason"
                                name="reason"
                                class="supplier-dashboard-input"
                                required>
                            <option value="">Pilih alasan retur</option>
                            <option value="damaged">Rusak</option>
                            <option value="expired">Kadaluarsa</option>
                            <option value="defective">Cacat</option>
                            <option value="overstock">Kelebihan Stok</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="resend_return_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi (Opsional)
                        </label>
                        <textarea id="resend_return_description"
                                  name="description"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Deskripsi untuk retur ulang..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeResendReturnModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Buat Retur Baru
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function openRespondModal(returnId) {
    const modal = document.getElementById('responseModal');
    const form = document.getElementById('responseForm');
    form.action = `/supplier/returns/${returnId}/respond`;
    modal.classList.add('active');
}

function closeRespondModal() {
    const modal = document.getElementById('responseModal');
    modal.classList.remove('active');
    document.getElementById('responseForm').reset();
}

function openResendModal(deliveryId, productName, quantity) {
    const modal = document.getElementById('resendModal');
    const form = document.getElementById('resendForm');
    const productNameElement = document.getElementById('resendProductName');
    const quantityElement = document.getElementById('resendQuantity');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function closeResendModal() {
    const modal = document.getElementById('resendModal');
    modal.classList.remove('active');
    document.getElementById('resendForm').reset();
}

function openDeleteCancelledModal(deliveryId, productName) {
    const modal = document.getElementById('deleteCancelledModal');
    const form = document.getElementById('deleteCancelledForm');
    const productNameElement = document.getElementById('deleteProductName');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteCancelledModal() {
    const modal = document.getElementById('deleteCancelledModal');
    modal.classList.remove('active');
}

function openDeleteReturnModal(returnId, productName) {
    const modal = document.getElementById('deleteReturnModal');
    const form = document.getElementById('deleteReturnForm');
    const productNameElement = document.getElementById('deleteReturnProductName');

    form.action = `/supplier/returns/${returnId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteReturnModal() {
    const modal = document.getElementById('deleteReturnModal');
    modal.classList.remove('active');
}

function openResendReturnModal(returnId, productName, quantity) {
    const modal = document.getElementById('resendReturnModal');
    const form = document.getElementById('resendReturnForm');
    const productNameElement = document.getElementById('resendReturnProductName');
    const quantityElement = document.getElementById('resendReturnQuantity');

    form.action = `/supplier/returns/${returnId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function closeResendReturnModal() {
    const modal = document.getElementById('resendReturnModal');
    modal.classList.remove('active');
    document.getElementById('resendReturnForm').reset();
}

// Close modals when clicking outside
document.getElementById('responseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRespondModal();
    }
});

document.getElementById('resendModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResendModal();
    }
});

document.getElementById('deleteCancelledModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteCancelledModal();
    }
});

document.getElementById('deleteReturnModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteReturnModal();
    }
});

document.getElementById('resendReturnModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResendReturnModal();
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.supplier', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/supplier/returns/index.blade.php ENDPATH**/ ?>